name: Reusable units test workflow

on:
  workflow_call:
    inputs:
      ref:
        description: GitHub ref to test.
        required: false
        type: string
        default: master
      nodeVersion:
        description: Version of node to use.
        required: false
        type: string
        default: 22.x
      cacheKey:
        description: Cache key for modules and build artifacts.
        required: false
        default: ''
        type: string
      collectCoverage:
        required: false
        default: false
        type: boolean
      ignoreTurboCache:
        required: false
        default: false
        type: boolean
      skipFrontendTests:
        required: false
        default: false
        type: boolean
    secrets:
      CODECOV_TOKEN:
        description: 'Codecov upload token.'
        required: false

jobs:
  unit-test:
    name: Unit tests
    runs-on: blacksmith-4vcpu-ubuntu-2204
    env:
      TURBO_FORCE: ${{ inputs.ignoreTurboCache }}
      COVERAGE_ENABLED: ${{ inputs.collectCoverage }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ inputs.ref }}

      - name: Setup Environment and Build Project
        uses: n8n-io/n8n/.github/actions/setup-and-build@7e870b8f7f5a39bb8bf82d1f42b6d44febc0082c # v1.100.1
        with:
          node-version: ${{ inputs.nodeVersion }}
          skip-build: ${{ inputs.cacheKey != '' }}

      - name: Restore cached build artifacts only
        if: ${{ inputs.cacheKey != '' }}
        uses: useblacksmith/cache/restore@c5fe29eb0efdf1cf4186b9f7fcbbcbc0cf025662 # v5.0.2
        with:
          path: ./packages/**/dist
          key: ${{ inputs.cacheKey }}
          fail-on-cache-miss: true

      - name: Test Backend
        run: pnpm test:backend

      - name: Test Nodes
        run: pnpm test:nodes

      - name: Test Frontend
        if: ${{ !inputs.skipFrontendTests }}
        run: pnpm test:frontend

      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@47f89e9acb64b76debcd5ea40642d25a4adced9f # v1.1.1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
