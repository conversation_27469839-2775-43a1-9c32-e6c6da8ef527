name: Reusable linting workflow

on:
  workflow_call:
    inputs:
      ref:
        description: GitHub ref to lint.
        required: false
        type: string
        default: master
      nodeVersion:
        description: Version of node to use.
        required: false
        type: string
        default: 22.x
      cacheKey:
        description: Cache key for modules and build artifacts.
        required: false
        type: string
        default: ''

jobs:
  lint:
    name: <PERSON>t
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ inputs.ref }}

      - name: Setup Environment
        uses: n8n-io/n8n/.github/actions/setup-and-build@7e870b8f7f5a39bb8bf82d1f42b6d44febc0082c # v1.100.1
        with:
          node-version: ${{ inputs.nodeVersion }}
          enable-caching: true
          skip-build: ${{ inputs.cacheKey != '' }}

      - name: Lint Backend
        run: pnpm lint:backend

      - name: <PERSON><PERSON> Nodes
        run: pnpm lint:nodes

      - name: <PERSON><PERSON> Frontend
        run: pnpm lint:frontend
