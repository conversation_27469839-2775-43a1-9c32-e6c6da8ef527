{"name": "@n8n/ai-workflow-builder", "version": "0.13.0", "scripts": {"clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build": "tsc -p ./tsconfig.build.json && tsc-alias -p tsconfig.build.json", "format": "biome format --write src", "format:check": "biome ci src", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "watch": "tsc-watch -p tsconfig.build.json --onCompilationComplete \"tsc-alias -p tsconfig.build.json\""}, "main": "dist/index.js", "module": "src/index.ts", "types": "dist/index.d.ts", "files": ["dist/**/*"], "exports": {".": {"require": "./dist/index.js", "import": "./src/index.ts", "types": "./dist/index.d.ts"}}, "dependencies": {"@langchain/anthropic": "catalog:", "@langchain/core": "catalog:", "@langchain/langgraph": "0.2.45", "@langchain/openai": "catalog:", "@n8n/config": "workspace:*", "@n8n/di": "workspace:*", "@n8n_io/ai-assistant-sdk": "catalog:", "n8n-workflow": "workspace:*", "zod": "catalog:"}, "devDependencies": {"@n8n/typescript-config": "workspace:*"}}