{"name": "@n8n/constants", "version": "0.9.0", "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "watch": "tsc -p tsconfig.build.json --watch"}, "main": "dist/index.js", "module": "src/index.ts", "types": "dist/index.d.ts", "files": ["dist/**/*"], "devDependencies": {"@n8n/typescript-config": "workspace:*"}}