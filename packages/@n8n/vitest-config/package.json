{"name": "@n8n/vitest-config", "version": "1.5.0", "type": "module", "peerDependencies": {"vite": "catalog:", "vitest": "catalog:"}, "devDependencies": {"@n8n/typescript-config": "workspace:*", "vite": "catalog:", "vitest": "catalog:"}, "files": ["backend.mjs", "frontend.mjs"], "exports": {"./backend": {"import": "./backend.mjs", "require": "./backend.mjs", "types": "./backend.d.ts"}, "./frontend": {"import": "./dist/frontend.js", "require": "./dist/frontend.js", "types": "./dist/frontend.d.ts"}, "./node": {"import": "./dist/node.js", "require": "./dist/node.js", "types": "./dist/node.d.ts"}}, "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json", "format": "biome format --write .", "format:check": "biome ci .", "watch": "tsc -p tsconfig.build.json --watch"}, "license": "See LICENSE.md file in the root of the repository"}